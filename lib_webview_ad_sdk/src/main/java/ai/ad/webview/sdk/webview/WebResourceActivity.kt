package ai.ad.webview.sdk.webview

import ai.ad.webview.sdk.R
import ai.ad.webview.sdk.WebResourceSDK
import ai.ad.webview.sdk.utils.Constants
import ai.ad.webview.sdk.utils.Logger
import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.WindowManager
import android.webkit.WebView


class WebResourceActivity : Activity() {
    private var mWebView: WebView? = null
    private var webDelegate: WebResourceDelegate? = null
    private var closeReceiver: BroadcastReceiver? = null
    
    companion object {
        private const val TAG = "WebResourceActivity"
        
        /**
         * 重启WebResourceActivity
         * 先关闭现有的Activity，然后启动新的Activity
         *
         * @param context 上下文
         */
        fun restart(context: Context?) {
            if (context == null) {
                Logger.e(TAG, "无法重启WebResourceActivity：context为null")
                return
            }
            
            // 首先发送关闭现有WebResourceActivity的广播
            WebResourceSDK.closeWebResourceActivityByNotify(context)
            Logger.d(TAG, "已发送关闭WebResourceActivity的广播")
            
            // 延迟一点时间，确保现有Activity有时间进行关闭
            Handler(Looper.getMainLooper()).postDelayed({
                // 然后启动新的WebResourceActivity
                val activityIntent = Intent(context, WebResourceActivity::class.java).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    // 添加这两个标志可以确保不会影响现有的任务栈
                    addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY)  // 不保留在历史栈中
                    addFlags(Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS)  // 不显示在最近任务中
                }
                context.startActivity(activityIntent)
                Logger.d(TAG, "通过restart函数成功启动WebResourceActivity, context=$context")
                WebResourceSDK.getInstance().notifyWebViewLoaded()
            }, 1000) // 1000ms的延迟应该足够让现有Activity关闭
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Logger.d(TAG, "WebResourceActivity onCreate开始")

        val window = this.window ?: return finish()
        window.setBackgroundDrawableResource(android.R.color.transparent)
        val params = window.attributes
        params.width = WindowManager.LayoutParams.MATCH_PARENT
        params.height = WindowManager.LayoutParams.MATCH_PARENT
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.statusBarColor = Color.TRANSPARENT // 设置状态栏透明
            window.navigationBarColor = Color.TRANSPARENT // 设置导航栏透明
        }
        // 设置窗口标志以允许点击穿透
        window.addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)

        initWebView()
        
        // 处理关闭广播
        closeReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    Constants.ACTION_BROADCAST_CLOSE_WEBVIEW -> finish()
                    else -> {}
                }
            }
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(
                closeReceiver, IntentFilter(Constants.ACTION_BROADCAST_CLOSE_WEBVIEW),
                Context.RECEIVER_NOT_EXPORTED
            )
        } else {
            registerReceiver(closeReceiver, IntentFilter(Constants.ACTION_BROADCAST_CLOSE_WEBVIEW))
        }

        // 直接执行脚本，因为启动前已经确保获取到了脚本数据
        Logger.d(TAG, "Activity已初始化完成，立即执行脚本")
        webDelegate?.executeScript()
    }
    
    @SuppressLint("ClickableViewAccessibility")
    private fun initWebView() {
        Logger.d(TAG, "初始化WebView")
        setContentView(R.layout.activity_webview)
        val webView = this.findViewById<WebView>(R.id.webView) ?: return finish()
        mWebView = webView
        webView.setOnTouchListener { _, _ -> false }
        webDelegate = WebResourceDelegate(this, webView)
        Logger.d(TAG, "WebView初始化完成")
    }

    override fun onResume() {
        super.onResume()
        Logger.d(TAG, "onResume -> 恢复WebView")
        mWebView?.onResume()
    }

    override fun onPause() {
        super.onPause()
        Logger.d(TAG, "onPause ->暂停WebView")
        mWebView?.onPause()
    }

    override fun onDestroy() {
        Logger.d(TAG, "WebResourceActivity onDestroy")
        try {
            closeReceiver?.let {
                unregisterReceiver(it)
                closeReceiver = null
            }
            mWebView?.destroy()
            mWebView = null
            webDelegate = null
        } catch (e: Exception) {
            Logger.e(TAG, "清理WebResourceActivity资源时出错", e)
        }
        
        super.onDestroy()
    }

    override fun finish() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 在API 21及以上版本，使用finishAndRemoveTask完全移除任务
            finishAndRemoveTask()
        } else {
            super.finish()
        }
    }
} 