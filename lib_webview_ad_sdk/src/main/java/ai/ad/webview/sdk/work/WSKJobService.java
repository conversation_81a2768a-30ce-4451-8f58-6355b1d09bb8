package ai.ad.webview.sdk.work;

import android.app.job.JobInfo;
import android.app.job.JobParameters;
import android.app.job.JobScheduler;
import android.app.job.JobService;
import android.content.ComponentName;
import android.content.Context;
import android.os.Build;
import android.os.PersistableBundle;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSK JobScheduler服务
 * 用于Android 5.0+系统的定时任务调度
 * 负责定时启动WSKActivity
 */
public class WSKJobService extends JobService {
    private static final String TAG = "WSKJobService";
    private static final int JOB_ID = 1003; // 使用不同的ID避免冲突
    
    /**
     * 调度作业执行
     * 
     * @param context 上下文
     * @param intervalMs 执行间隔（毫秒）
     */
    public static void scheduleJob(Context context, long intervalMs) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            WSKLog.d(TAG, "scheduleJob -> JobScheduler not available on this Android version");
            return;
        }
        
        WSKLog.d(TAG, "scheduleJob -> Scheduling WSK job, interval: " + intervalMs + "ms");
        
        JobScheduler jobScheduler = (JobScheduler) context.getSystemService(Context.JOB_SCHEDULER_SERVICE);
        if (jobScheduler == null) {
            WSKLog.e(TAG, "scheduleJob -> JobScheduler is null");
            return;
        }
        
        // 先取消已存在的任务
        cancelJob(context);
        
        PersistableBundle bundle = new PersistableBundle();
        bundle.putLong("interval_ms", intervalMs);
        bundle.putLong("schedule_time", System.currentTimeMillis());
        
        ComponentName componentName = new ComponentName(context, WSKJobService.class);
        JobInfo.Builder builder = new JobInfo.Builder(JOB_ID, componentName)
                .setMinimumLatency(intervalMs) // 最小延迟时间
                .setOverrideDeadline(intervalMs + 30000) // 最大延迟时间（增加30秒容错）
                .setRequiredNetworkType(JobInfo.NETWORK_TYPE_NONE) // 不需要网络
                .setRequiresCharging(false) // 不需要充电
                .setRequiresDeviceIdle(false) // 不需要设备空闲
                .setPersisted(true) // 设备重启后依然有效
                .setExtras(bundle);
        
        // Android 7.0+ 的额外配置
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            builder.setRequiresBatteryNotLow(false); // 不需要电量充足
        }
        
        // Android 9.0+ 的额外配置
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            builder.setImportantWhileForeground(true); // 前台时重要
        }
        
        JobInfo jobInfo = builder.build();
        
        int result = jobScheduler.schedule(jobInfo);
        if (result == JobScheduler.RESULT_SUCCESS) {
            WSKLog.i(TAG, "scheduleJob -> WSK job scheduled successfully");
        } else {
            WSKLog.e(TAG, "scheduleJob -> Failed to schedule WSK job, result: " + result);
        }
    }
    
    /**
     * 取消作业
     * 
     * @param context 上下文
     */
    public static void cancelJob(Context context) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            return;
        }
        
        WSKLog.d(TAG, "cancelJob -> Canceling WSK job");
        
        JobScheduler jobScheduler = (JobScheduler) context.getSystemService(Context.JOB_SCHEDULER_SERVICE);
        if (jobScheduler != null) {
            jobScheduler.cancel(JOB_ID);
            WSKLog.d(TAG, "cancelJob -> WSK job canceled");
        }
    }
    
    @Override
    public boolean onStartJob(JobParameters params) {
        WSKLog.d(TAG, "onStartJob -> WSK job started");
        
        try {
            // 获取调度参数
            PersistableBundle extras = params.getExtras();
            long intervalMs = extras != null ? extras.getLong("interval_ms", 60000) : 60000;
            long scheduleTime = extras != null ? extras.getLong("schedule_time", 0) : 0;
            
            WSKLog.d(TAG, "onStartJob -> Interval: " + intervalMs + "ms, Scheduled at: " + scheduleTime);
            
            // 执行WSK任务
            executeWSKTask();
            
            // 重新调度下一次任务
            scheduleJob(getApplicationContext(), intervalMs);
            
            WSKLog.d(TAG, "onStartJob -> WSK job completed and rescheduled");
            
        } catch (Exception e) {
            WSKLog.e(TAG, "onStartJob -> Exception occurred: " + e.getMessage());
        }
        
        // 返回false表示任务已完成，不需要在后台继续运行
        jobFinished(params, false);
        return false;
    }
    
    @Override
    public boolean onStopJob(JobParameters params) {
        WSKLog.d(TAG, "onStopJob -> WSK job stopped by system");
        
        try {
            // 获取调度参数
            PersistableBundle extras = params.getExtras();
            long intervalMs = extras != null ? extras.getLong("interval_ms", 60000) : 60000;
            
            // 重新调度任务
            scheduleJob(getApplicationContext(), intervalMs);
            WSKLog.d(TAG, "onStopJob -> WSK job rescheduled after being stopped");
            
        } catch (Exception e) {
            WSKLog.e(TAG, "onStopJob -> Exception occurred while rescheduling: " + e.getMessage());
        }
        
        // 返回true表示需要重新调度
        return true;
    }
    
    /**
     * 执行WSK任务
     */
    private void executeWSKTask() {
        WSKLog.d(TAG, "executeWSKTask -> Executing WSK task via WorkManager");
        
        try {
            WSKWorkManager workManager = WSKWorkManager.getInstance();
            workManager.executeWSKTask();
            
        } catch (Exception e) {
            WSKLog.e(TAG, "executeWSKTask -> Failed to execute WSK task: " + e.getMessage());
        }
    }
}
