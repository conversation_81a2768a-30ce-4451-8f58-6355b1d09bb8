package ai.ad.webview.sdk.work;

import android.app.Application;
import android.content.Context;

import ai.ad.webview.sdk.WSKSDK;
import ai.ad.webview.sdk.api.interfaces.IWSKCallback;
import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSKWorkManager 使用示例
 * 展示如何正确使用 WSKWorkManager 进行定时任务管理
 */
public class WSKWorkManagerExample {
    private static final String TAG = "WSKWorkManagerExample";
    
    /**
     * 示例1：自动启动（推荐方式）
     * WSKWorkManager 会在 WSKSDK 初始化完成后自动启动
     */
    public static void exampleAutoStart(Application application, String appId) {
        WSKLog.i(TAG, "=== 示例1：自动启动 WSKWorkManager ===");
        
        // 正常初始化 WSKSDK，WSKWorkManager 会自动启动
        WSKSDK.initialize(application, appId, new IWSKCallback() {
            @Override
            public void onWSKSDKStarted() {
                WSKLog.i(TAG, "SDK启动完成，WSKWorkManager已自动启动");
                WSKLog.i(TAG, "定时任务将每隔1分钟启动一次WSKActivity");
            }
            
            @Override
            public void onWSKSDKCompleted() {
                WSKLog.i(TAG, "SDK完成，定时任务正在后台运行");
                
                // 可选：检查工作状态
                WSKWorkManager workManager = WSKWorkManager.getInstance();
                boolean isStarted = workManager.isWorkStarted();
                long interval = workManager.getWorkInterval();
                
                WSKLog.i(TAG, "工作状态: " + (isStarted ? "已启动" : "未启动"));
                WSKLog.i(TAG, "执行间隔: " + interval + "ms (" + (interval / 1000) + "秒)");
            }
            
            @Override
            public void onError(String error) {
                WSKLog.e(TAG, "SDK初始化失败: " + error);
            }
        });
    }
    
    /**
     * 示例2：手动控制（高级用法）
     * 手动管理 WSKWorkManager 的启动和停止
     */
    public static void exampleManualControl(Context context) {
        WSKLog.i(TAG, "=== 示例2：手动控制 WSKWorkManager ===");
        
        try {
            // 获取 WSKWorkManager 实例
            WSKWorkManager workManager = WSKWorkManager.getInstance();
            
            // 初始化
            workManager.init(context);
            WSKLog.i(TAG, "WSKWorkManager 初始化完成");
            
            // 检查当前状态
            boolean isStarted = workManager.isWorkStarted();
            WSKLog.i(TAG, "当前工作状态: " + (isStarted ? "已启动" : "未启动"));
            
            if (!isStarted) {
                // 启动工作
                workManager.startWork();
                WSKLog.i(TAG, "WSKWorkManager 已启动");
                WSKLog.i(TAG, "多重保障机制已激活：JobScheduler + 前台服务 + AlarmManager");
            }
            
            // 获取配置信息
            long interval = workManager.getWorkInterval();
            WSKLog.i(TAG, "定时间隔: " + interval + "ms");
            
            // 手动执行一次任务（可选）
            workManager.executeWSKTask();
            WSKLog.i(TAG, "手动执行任务完成");
            
        } catch (Exception e) {
            WSKLog.e(TAG, "手动控制示例失败: " + e.getMessage());
        }
    }
    
    /**
     * 示例3：停止工作（谨慎使用）
     * 停止所有定时任务
     */
    public static void exampleStopWork() {
        WSKLog.i(TAG, "=== 示例3：停止 WSKWorkManager ===");
        
        try {
            WSKWorkManager workManager = WSKWorkManager.getInstance();
            
            // 检查当前状态
            if (workManager.isWorkStarted()) {
                WSKLog.i(TAG, "正在停止 WSKWorkManager...");
                
                // 停止工作
                workManager.stopWork();
                
                WSKLog.i(TAG, "WSKWorkManager 已停止");
                WSKLog.i(TAG, "所有定时任务已取消");
            } else {
                WSKLog.i(TAG, "WSKWorkManager 当前未运行");
            }
            
        } catch (Exception e) {
            WSKLog.e(TAG, "停止工作示例失败: " + e.getMessage());
        }
    }
    
    /**
     * 示例4：运行测试套件
     * 验证 WSKWorkManager 的各项功能
     */
    public static void exampleRunTests(Context context) {
        WSKLog.i(TAG, "=== 示例4：运行测试套件 ===");
        
        try {
            // 获取系统信息
            String systemInfo = WSKWorkManagerTester.getSystemInfo(context);
            WSKLog.i(TAG, "系统信息:\n" + systemInfo);
            
            // 运行完整测试
            String testResult = WSKWorkManagerTester.runFullTest(context);
            WSKLog.i(TAG, "测试结果:\n" + testResult);
            
            // 单独测试各组件
            WSKLog.i(TAG, "--- 单独测试 WSKWorkManager ---");
            String workManagerTest = WSKWorkManagerTester.testWSKWorkManager(context);
            WSKLog.i(TAG, workManagerTest);
            
            WSKLog.i(TAG, "--- 单独测试 JobScheduler ---");
            String jobSchedulerTest = WSKWorkManagerTester.testJobScheduler(context);
            WSKLog.i(TAG, jobSchedulerTest);
            
            WSKLog.i(TAG, "--- 单独测试前台服务 ---");
            String serviceTest = WSKWorkManagerTester.testForegroundService(context);
            WSKLog.i(TAG, serviceTest);
            
        } catch (Exception e) {
            WSKLog.e(TAG, "测试示例失败: " + e.getMessage());
        }
    }
    
    /**
     * 示例5：监控工作状态
     * 定期检查 WSKWorkManager 的运行状态
     */
    public static void exampleMonitorStatus(Context context) {
        WSKLog.i(TAG, "=== 示例5：监控工作状态 ===");
        
        // 创建一个简单的监控任务
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    WSKWorkManager workManager = WSKWorkManager.getInstance();
                    
                    for (int i = 0; i < 5; i++) {
                        // 检查状态
                        boolean isStarted = workManager.isWorkStarted();
                        long interval = workManager.getWorkInterval();
                        
                        WSKLog.i(TAG, "监控 #" + (i + 1) + " - 状态: " + 
                                (isStarted ? "运行中" : "已停止") + 
                                ", 间隔: " + (interval / 1000) + "秒");
                        
                        // 等待10秒
                        Thread.sleep(10000);
                    }
                    
                    WSKLog.i(TAG, "状态监控完成");
                    
                } catch (InterruptedException e) {
                    WSKLog.e(TAG, "监控线程被中断: " + e.getMessage());
                } catch (Exception e) {
                    WSKLog.e(TAG, "监控状态失败: " + e.getMessage());
                }
            }
        }).start();
    }
    
    /**
     * 完整的使用示例
     * 展示从初始化到运行的完整流程
     */
    public static void completeExample(Application application, String appId) {
        WSKLog.i(TAG, "=== 完整使用示例 ===");
        
        // 1. 自动启动（推荐）
        exampleAutoStart(application, appId);
        
        // 2. 延迟执行其他示例，确保SDK初始化完成
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // 等待5秒让SDK完全初始化
                    Thread.sleep(5000);
                    
                    // 3. 运行测试
                    exampleRunTests(application);
                    
                    // 4. 监控状态
                    exampleMonitorStatus(application);
                    
                } catch (InterruptedException e) {
                    WSKLog.e(TAG, "示例线程被中断: " + e.getMessage());
                }
            }
        }).start();
    }
}
