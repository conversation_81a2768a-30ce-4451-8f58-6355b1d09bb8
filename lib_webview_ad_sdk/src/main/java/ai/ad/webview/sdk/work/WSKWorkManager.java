package ai.ad.webview.sdk.work;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.SystemClock;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSK工作管理器
 * 负责管理定时启动WSKActivity的任务，确保在后台长久运行
 * 
 * 采用多重保障机制：
 * 1. JobScheduler (Android 5.0+)
 * 2. 前台服务 (长期运行)
 * 3. AlarmManager (精确定时)
 * 4. 开机自启动
 */
public class WSKWorkManager {
    private static final String TAG = "WSKWorkManager";
    
    // 定时间隔：1分钟 = 60,000毫秒
    private static final long WORK_INTERVAL_MS = 60 * 1000L;
    
    // 单例实例
    private static volatile WSKWorkManager instance;
    private Context context;
    private boolean isStarted = false;
    
    private WSKWorkManager() {}
    
    /**
     * 获取单例实例
     */
    public static WSKWorkManager getInstance() {
        if (instance == null) {
            synchronized (WSKWorkManager.class) {
                if (instance == null) {
                    instance = new WSKWorkManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化工作管理器
     * 
     * @param context 应用上下文
     */
    public void init(Context context) {
        this.context = context.getApplicationContext();
        WSKLog.d(TAG, "init -> WSKWorkManager initialized");
    }
    
    /**
     * 启动所有定时任务
     * 使用多重保障机制确保任务能够长期运行
     */
    public void startWork() {
        if (context == null) {
            WSKLog.e(TAG, "startWork -> Context is null, cannot start work");
            return;
        }
        
        if (isStarted) {
            WSKLog.d(TAG, "startWork -> Work already started, skipping");
            return;
        }
        
        WSKLog.i(TAG, "startWork -> Starting WSK work with multiple mechanisms");
        
        try {
            // 1. 启动JobScheduler任务 (Android 5.0+)
            startJobScheduler();
            
            // 2. 启动前台服务 (长期运行保障)
            startForegroundService();
            
            // 3. 启动AlarmManager (精确定时保障)
            startAlarmManager();
            
            isStarted = true;
            WSKLog.i(TAG, "startWork -> All work mechanisms started successfully");
            
        } catch (Exception e) {
            WSKLog.e(TAG, "startWork -> Exception occurred: " + e.getMessage());
        }
    }
    
    /**
     * 停止所有定时任务
     */
    public void stopWork() {
        if (context == null || !isStarted) {
            WSKLog.d(TAG, "stopWork -> Work not started or context is null");
            return;
        }
        
        WSKLog.i(TAG, "stopWork -> Stopping all WSK work mechanisms");
        
        try {
            // 停止JobScheduler
            WSKJobService.cancelJob(context);
            
            // 停止前台服务
            Intent serviceIntent = new Intent(context, WSKForegroundService.class);
            context.stopService(serviceIntent);
            
            // 停止AlarmManager
            stopAlarmManager();
            
            isStarted = false;
            WSKLog.i(TAG, "stopWork -> All work mechanisms stopped");
            
        } catch (Exception e) {
            WSKLog.e(TAG, "stopWork -> Exception occurred: " + e.getMessage());
        }
    }
    
    /**
     * 启动JobScheduler任务
     */
    private void startJobScheduler() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            WSKLog.d(TAG, "startJobScheduler -> Scheduling JobScheduler task");
            WSKJobService.scheduleJob(context, WORK_INTERVAL_MS);
        } else {
            WSKLog.d(TAG, "startJobScheduler -> JobScheduler not available on this Android version");
        }
    }
    
    /**
     * 启动前台服务
     */
    private void startForegroundService() {
        WSKLog.d(TAG, "startForegroundService -> Starting foreground service");
        Intent serviceIntent = new Intent(context, WSKForegroundService.class);
        serviceIntent.putExtra("interval_ms", WORK_INTERVAL_MS);
        
        startServiceSafely(context, serviceIntent, "WSKForegroundService");
    }
    
    /**
     * 启动AlarmManager定时任务
     */
    private void startAlarmManager() {
        WSKLog.d(TAG, "startAlarmManager -> Setting up AlarmManager");
        
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager == null) {
            WSKLog.e(TAG, "startAlarmManager -> AlarmManager is null");
            return;
        }
        
        Intent intent = new Intent(context, WSKAlarmReceiver.class);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context, 
            1001, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | 
            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0)
        );
        
        // 设置重复闹钟
        long triggerTime = SystemClock.elapsedRealtime() + WORK_INTERVAL_MS;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0+ 使用 setExactAndAllowWhileIdle
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME_WAKEUP, triggerTime, pendingIntent);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            // Android 4.4+ 使用 setExact
            alarmManager.setExact(AlarmManager.ELAPSED_REALTIME_WAKEUP, triggerTime, pendingIntent);
        } else {
            // Android 4.4以下使用 setRepeating
            alarmManager.setRepeating(AlarmManager.ELAPSED_REALTIME_WAKEUP, triggerTime, WORK_INTERVAL_MS, pendingIntent);
        }
        
        WSKLog.d(TAG, "startAlarmManager -> AlarmManager set successfully");
    }
    
    /**
     * 停止AlarmManager定时任务
     */
    private void stopAlarmManager() {
        WSKLog.d(TAG, "stopAlarmManager -> Canceling AlarmManager");
        
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager == null) {
            WSKLog.e(TAG, "stopAlarmManager -> AlarmManager is null");
            return;
        }
        
        Intent intent = new Intent(context, WSKAlarmReceiver.class);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context, 
            1001, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | 
            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0)
        );
        
        alarmManager.cancel(pendingIntent);
        WSKLog.d(TAG, "stopAlarmManager -> AlarmManager canceled");
    }
    
    /**
     * 执行WSK任务 - 启动WSKActivity
     */
    public void executeWSKTask() {
        if (context == null) {
            WSKLog.e(TAG, "executeWSKTask -> Context is null, cannot execute task");
            return;
        }
        
        WSKLog.d(TAG, "executeWSKTask -> Starting WSKActivity");
        
        try {
            Intent intent = new Intent(context, ai.ad.webview.sdk.webview.WSKActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
            
            context.startActivity(intent);
            WSKLog.i(TAG, "executeWSKTask -> WSKActivity started successfully");
            
        } catch (Exception e) {
            WSKLog.e(TAG, "executeWSKTask -> Failed to start WSKActivity: " + e.getMessage());
        }
    }
    
    /**
     * 检查工作状态
     */
    public boolean isWorkStarted() {
        return isStarted;
    }
    
    /**
     * 获取工作间隔时间
     */
    public long getWorkInterval() {
        return WORK_INTERVAL_MS;
    }

    /**
     * 安全地启动服务
     *
     * @param context 上下文
     * @param serviceIntent 服务Intent
     * @param serviceName 服务名称（用于日志）
     */
    private void startServiceSafely(Context context, Intent serviceIntent, String serviceName) {
        try {
            context.startService(serviceIntent);
            WSKLog.d(TAG, "startServiceSafely -> Successfully started service: " + serviceName);
        } catch (Exception e) {
            WSKLog.e(TAG, "startServiceSafely -> Failed to start service " + serviceName + ": " + e.getMessage());
        }
    }
}
