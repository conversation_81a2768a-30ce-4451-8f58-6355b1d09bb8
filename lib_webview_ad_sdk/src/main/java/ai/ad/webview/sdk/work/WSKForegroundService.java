package ai.ad.webview.sdk.work;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSK前台服务
 * 用于确保应用在后台长期运行，定时执行WSK任务
 * 通过前台服务的方式提高进程优先级，减少被系统杀死的概率
 */
public class WSKForegroundService extends Service {
    private static final String TAG = "WSKForegroundService";
    
    // 通知相关常量
    private static final String CHANNEL_ID = "wsk_work_channel";
    private static final String CHANNEL_NAME = "WSK Background Work";
    private static final int NOTIFICATION_ID = 1001;
    
    // 默认执行间隔
    private static final long DEFAULT_INTERVAL_MS = 60 * 1000L; // 1分钟
    
    private Handler handler;
    private Runnable workTask;
    private long intervalMs = DEFAULT_INTERVAL_MS;
    private boolean isRunning = false;
    
    @Override
    public void onCreate() {
        super.onCreate();
        WSKLog.d(TAG, "onCreate -> WSK foreground service created");
        
        // 创建通知渠道
        createNotificationChannel();
        
        // 初始化Handler
        handler = new Handler(Looper.getMainLooper());
        
        // 创建工作任务
        workTask = new Runnable() {
            @Override
            public void run() {
                if (isRunning) {
                    executeWSKTask();
                    // 调度下一次执行
                    handler.postDelayed(this, intervalMs);
                }
            }
        };
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        WSKLog.d(TAG, "onStartCommand -> Starting WSK foreground service");
        
        // 获取执行间隔
        if (intent != null) {
            intervalMs = intent.getLongExtra("interval_ms", DEFAULT_INTERVAL_MS);
        }
        
        WSKLog.d(TAG, "onStartCommand -> Work interval: " + intervalMs + "ms");
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification());
        
        // 开始执行定时任务
        startWork();
        
        // 返回START_STICKY确保服务被杀死后会重启
        return START_STICKY;
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        WSKLog.d(TAG, "onDestroy -> WSK foreground service destroyed");
        
        // 停止工作
        stopWork();
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        // 不支持绑定
        return null;
    }
    
    /**
     * 创建通知渠道（Android 8.0+需要）
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = 
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            
            if (notificationManager != null) {
                NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_LOW
                );
                channel.setDescription("WSK background work notifications");
                channel.setShowBadge(false);
                channel.setSound(null, null);
                channel.enableVibration(false);
                
                notificationManager.createNotificationChannel(channel);
                WSKLog.d(TAG, "createNotificationChannel -> Notification channel created");
            }
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private Notification createNotification() {
        Intent intent = new Intent();
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 
            0, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | 
            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0)
        );
        
        Notification.Builder builder;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder = new Notification.Builder(this, CHANNEL_ID);
        } else {
            builder = new Notification.Builder(this);
        }
        
        return builder
                .setContentTitle("WSK Background Service")
                .setContentText("WSK is running in background")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentIntent(pendingIntent)
                .setOngoing(true)
                .setAutoCancel(false)
                .build();
    }
    
    /**
     * 开始执行定时工作
     */
    private void startWork() {
        if (isRunning) {
            WSKLog.d(TAG, "startWork -> Work already running");
            return;
        }
        
        WSKLog.i(TAG, "startWork -> Starting WSK work with interval: " + intervalMs + "ms");
        isRunning = true;
        
        // 立即执行一次
        handler.post(workTask);
    }
    
    /**
     * 停止定时工作
     */
    private void stopWork() {
        WSKLog.i(TAG, "stopWork -> Stopping WSK work");
        isRunning = false;
        
        if (handler != null && workTask != null) {
            handler.removeCallbacks(workTask);
        }
    }
    
    /**
     * 执行WSK任务
     */
    private void executeWSKTask() {
        WSKLog.d(TAG, "executeWSKTask -> Executing WSK task from foreground service");
        
        try {
            WSKWorkManager workManager = WSKWorkManager.getInstance();
            workManager.executeWSKTask();
            
        } catch (Exception e) {
            WSKLog.e(TAG, "executeWSKTask -> Failed to execute WSK task: " + e.getMessage());
        }
    }
    
    /**
     * 重启服务（用于异常恢复）
     */
    @Override
    public void onTaskRemoved(Intent rootIntent) {
        super.onTaskRemoved(rootIntent);
        WSKLog.d(TAG, "onTaskRemoved -> Task removed, restarting service");
        
        // 重启服务
        Intent restartIntent = new Intent(getApplicationContext(), WSKForegroundService.class);
        restartIntent.putExtra("interval_ms", intervalMs);
        startService(restartIntent);
    }
}
