package ai.ad.webview.sdk.webview

import ai.ad.webview.sdk.WebResourceSDK
import ai.ad.webview.sdk.control.ScriptControl
import ai.ad.webview.sdk.repository.EventRepo
import ai.ad.webview.sdk.repository.ScriptRepo
import ai.ad.webview.sdk.utils.DeviceIdUtil
import ai.ad.webview.sdk.utils.Logger
import android.annotation.SuppressLint
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.IBinder

class WebResourceService : Service() {
    // WebResourceOverlay实例，延迟创建
    private var overlay: WebResourceOverlay? = null

    // 启动Activity的广播接收器
    private val launchActivityReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Logger.d(TAG, "接收到启动Activity的广播")
            try {
                // 使用WebResourceActivity的restart方法重启Activity
                WebResourceActivity.restart(context)
            } catch (e: Exception) {
                Logger.e(TAG, "通过广播启动WebResourceActivity失败", e)
            }
        }
    }

    companion object {
        private const val TAG = "WebResourceService"
        private const val ACTION_LAUNCH_ACTIVITY = "ai.ad.webview.sdk.ACTION_LAUNCH_ACTIVITY"
    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    override fun onCreate() {
        super.onCreate()
        Logger.d(TAG, "WebResourceService created")

        // 初始化ReportRepo
        EventRepo.init(applicationContext)

        // 注册启动Activity的广播接收器
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(
                launchActivityReceiver,
                IntentFilter(ACTION_LAUNCH_ACTIVITY),
                Context.RECEIVER_EXPORTED
            )
        } else {
            registerReceiver(launchActivityReceiver, IntentFilter(ACTION_LAUNCH_ACTIVITY))
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Logger.d(TAG, "onStartCommand called, intent=${intent != null}, service starting new task")

        // 更新脚本计划并获取脚本数据
        updateScriptPlanAndProceed()

        return START_STICKY
    }

    /**
     * 更新脚本计划并根据结果决定后续步骤
     */
    private fun updateScriptPlanAndProceed() {
        Logger.d(TAG, "Starting to update script plan...")

        // 从WebResourceSDK获取应用ID
        val adAppId = WebResourceSDK.getAppId()
        val deviceId = DeviceIdUtil.getDeviceId()
        Logger.d(TAG, "Using appId: $adAppId, deviceId: $deviceId")

        // 如果设备ID为空，等待一段时间后再尝试
        if (deviceId.isEmpty()) {
            Logger.d(TAG, "DeviceId is empty, retrying after 500ms...")

            var waitTime = 0
            val maxWaitTime = 3000 // 最多等待3000ms
            val waitInterval = 200 // 每次等待500ms

            // 创建一个递归函数来处理等待和重试
            fun retryAfterWait() {
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    waitTime += waitInterval
                    val newDeviceId = DeviceIdUtil.getDeviceId()
                    Logger.d(TAG, "After waiting ${waitTime}ms, deviceId: $newDeviceId")

                    if (newDeviceId.isNotEmpty() || waitTime >= maxWaitTime) {
                        // 设备ID已获取到或者已达到最大等待时间，继续执行
                        proceedWithScriptUpdate(adAppId)
                    } else {
                        // 继续等待
                        retryAfterWait()
                    }
                }, waitInterval.toLong())
            }

            // 开始等待和重试
            retryAfterWait()
        } else {
            // 设备ID已存在，直接继续
            proceedWithScriptUpdate(adAppId)
        }
    }

    /**
     * 继续执行脚本更新流程
     */
    private fun proceedWithScriptUpdate(adAppId: String) {
        val deviceId = DeviceIdUtil.getDeviceId()
        Logger.d(TAG, "Proceeding with script update, appId: $adAppId, deviceId: $deviceId")

        // 使用新的简化方法获取脚本数据
        ScriptRepo.updatePlanAndSelectScriptData(applicationContext, adAppId) { scriptData ->
            // 无论脚本数据获取成功与否，都设置下次执行的间隔
            scheduleNextExecution()

            if (scriptData == null) {
                Logger.e(TAG, "Failed to get script data, waiting for next task")
                return@updatePlanAndSelectScriptData
            }
            val scriptControl = ScriptControl.getInstance(applicationContext)
            // 记录脚本执行
            val executionCount = scriptControl.recordScriptExecution(scriptData)
            Logger.d(TAG, "Recording script[${scriptData.metadata.id}] execution, current count: $executionCount")

            ScriptRepo.setScriptData(scriptData)
            // 尝试创建悬浮窗
            tryCreateOverlayOrFallbackToActivity()
        }
    }

    /**
     * 尝试创建悬浮窗，如果失败则降级使用Activity
     */
    private fun tryCreateOverlayOrFallbackToActivity() {
        try {
            Logger.d(TAG, "Trying to create overlay")

            // 销毁现有的悬浮窗实例
            overlay?.destroy()
            // 创建WebResourceOverlay实例
            overlay = WebResourceOverlay(this)

            // 使用优先级策略创建悬浮窗
            val success = overlay?.tryCreateOverlay() ?: false

            // 保存悬浮窗创建结果
            ScriptRepo.setOverlayCreationSuccess(applicationContext, success)

            if (!success) {
                // 如果悬浮窗创建失败，降级使用WebActivity
                Logger.d(TAG, "Overlay creation failed, falling back to WebActivity")
                if (WebResourceSDK.isAttach()){
                    fallbackToWebActivity()
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "Exception when creating overlay, falling back to WebActivity: ${e.message}", e)
            ScriptRepo.setOverlayCreationSuccess(applicationContext, false)
            fallbackToWebActivity()
        }
    }

    /**
     * 最终降级方案：使用WebActivity
     */
    private fun fallbackToWebActivity() {
        Logger.d(TAG, "Using WebActivity as final fallback")
        ScriptRepo.setOverlayCreationSuccess(applicationContext, false)
        launchWebResourceActivity()
    }

    private fun scheduleNextExecution() {
        // 从脚本计划中获取间隔时间
        val intervalMs = ScriptRepo.getScriptPlan().mockConfig.interval * 1000L

        Logger.i(TAG, "Scheduling next execution, interval: ${intervalMs}ms")

        // 取消已存在的JobScheduler任务，然后创建新任务
        WebResourceJobService.cancelJob(applicationContext)
        WebResourceJobService.scheduleJob(applicationContext, intervalMs)
    }

    override fun onDestroy() {
        Logger.d(TAG, "WebResourceService is being destroyed")

        // 注销广播接收器
        try {
            unregisterReceiver(launchActivityReceiver)
            Logger.d(TAG, "Broadcast receiver unregistered successfully")
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to unregister broadcast receiver", e)
        }

        // 清理WebResourceOverlay资源
        overlay?.destroy()
        overlay = null

        // 取消JobScheduler任务
        WebResourceJobService.cancelJob(applicationContext)

        WebResourceSDK.getInstance().notifyServiceStopped()
        super.onDestroy()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    /**
     * 通过WebResourceActivity.restart方法启动Activity
     */
    private fun launchWebResourceActivity() {
        try {
            // 直接使用WebResourceActivity的restart方法
            WebResourceActivity.restart(applicationContext)
            Logger.d(TAG, "Called WebResourceActivity.restart to launch activity")
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to launch WebResourceActivity", e)
        }
    }
}