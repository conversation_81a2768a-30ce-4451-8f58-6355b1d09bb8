package ai.ad.webview.sdk.work;

import android.content.Context;
import android.content.Intent;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSK工作管理器测试类
 * 用于测试和验证WSKWorkManager的各项功能
 */
public class WSKWorkManagerTester {
    private static final String TAG = "WSKWorkManagerTester";
    
    /**
     * 测试WSKWorkManager的基本功能
     * 
     * @param context 上下文
     * @return 测试结果
     */
    public static String testWSKWorkManager(Context context) {
        StringBuilder result = new StringBuilder();
        result.append("=== WSK Work Manager Test ===\n");
        
        try {
            // 1. 测试单例获取
            WSKWorkManager workManager = WSKWorkManager.getInstance();
            if (workManager != null) {
                result.append("✓ Singleton instance created successfully\n");
            } else {
                result.append("✗ Failed to create singleton instance\n");
                return result.toString();
            }
            
            // 2. 测试初始化
            workManager.init(context);
            result.append("✓ WorkManager initialized\n");
            
            // 3. 测试工作状态检查
            boolean isStarted = workManager.isWorkStarted();
            result.append("✓ Work status check: " + (isStarted ? "Started" : "Not started") + "\n");
            
            // 4. 测试获取工作间隔
            long interval = workManager.getWorkInterval();
            result.append("✓ Work interval: " + interval + "ms (" + (interval / 1000) + " seconds)\n");
            
            // 5. 测试启动工作
            workManager.startWork();
            result.append("✓ Work started successfully\n");
            
            // 6. 再次检查工作状态
            isStarted = workManager.isWorkStarted();
            result.append("✓ Work status after start: " + (isStarted ? "Started" : "Not started") + "\n");
            
            // 7. 测试手动执行任务
            workManager.executeWSKTask();
            result.append("✓ Manual task execution completed\n");
            
            result.append("\n=== Test Summary ===\n");
            result.append("All tests passed successfully!\n");
            result.append("WSKWorkManager is ready for production use.\n");
            
        } catch (Exception e) {
            result.append("✗ Test failed with exception: ").append(e.getMessage()).append("\n");
            WSKLog.e(TAG, "testWSKWorkManager -> Exception: " + e.getMessage());
        }
        
        return result.toString();
    }
    
    /**
     * 测试JobScheduler功能
     * 
     * @param context 上下文
     * @return 测试结果
     */
    public static String testJobScheduler(Context context) {
        StringBuilder result = new StringBuilder();
        result.append("=== JobScheduler Test ===\n");
        
        try {
            // 测试调度任务
            WSKJobService.scheduleJob(context, 60000);
            result.append("✓ JobScheduler task scheduled successfully\n");
            
            // 测试取消任务
            WSKJobService.cancelJob(context);
            result.append("✓ JobScheduler task canceled successfully\n");
            
            result.append("JobScheduler test completed!\n");
            
        } catch (Exception e) {
            result.append("✗ JobScheduler test failed: ").append(e.getMessage()).append("\n");
            WSKLog.e(TAG, "testJobScheduler -> Exception: " + e.getMessage());
        }
        
        return result.toString();
    }
    
    /**
     * 测试前台服务功能
     * 
     * @param context 上下文
     * @return 测试结果
     */
    public static String testForegroundService(Context context) {
        StringBuilder result = new StringBuilder();
        result.append("=== Foreground Service Test ===\n");
        
        try {
            // 启动前台服务
            Intent serviceIntent = new Intent(context, WSKForegroundService.class);
            serviceIntent.putExtra("interval_ms", 60000L);
            context.startService(serviceIntent);
            result.append("✓ Foreground service started successfully\n");
            
            // 等待一段时间后停止服务
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(5000); // 等待5秒
                        Intent stopIntent = new Intent(context, WSKForegroundService.class);
                        context.stopService(stopIntent);
                        WSKLog.d(TAG, "testForegroundService -> Service stopped after 5 seconds");
                    } catch (InterruptedException e) {
                        WSKLog.e(TAG, "testForegroundService -> Thread interrupted: " + e.getMessage());
                    }
                }
            }).start();
            
            result.append("✓ Foreground service will be stopped after 5 seconds\n");
            result.append("Foreground service test completed!\n");
            
        } catch (Exception e) {
            result.append("✗ Foreground service test failed: ").append(e.getMessage()).append("\n");
            WSKLog.e(TAG, "testForegroundService -> Exception: " + e.getMessage());
        }
        
        return result.toString();
    }
    
    /**
     * 运行完整的测试套件
     * 
     * @param context 上下文
     * @return 完整测试结果
     */
    public static String runFullTest(Context context) {
        StringBuilder fullResult = new StringBuilder();
        
        WSKLog.i(TAG, "runFullTest -> Starting full WSK Work Manager test suite");
        
        // 1. 测试WSKWorkManager
        fullResult.append(testWSKWorkManager(context)).append("\n");
        
        // 2. 测试JobScheduler
        fullResult.append(testJobScheduler(context)).append("\n");
        
        // 3. 测试前台服务
        fullResult.append(testForegroundService(context)).append("\n");
        
        fullResult.append("=== Full Test Suite Completed ===\n");
        
        WSKLog.i(TAG, "runFullTest -> Full test suite completed");
        
        return fullResult.toString();
    }
    
    /**
     * 获取系统信息用于调试
     * 
     * @param context 上下文
     * @return 系统信息
     */
    public static String getSystemInfo(Context context) {
        StringBuilder info = new StringBuilder();
        info.append("=== System Information ===\n");
        
        try {
            info.append("Android Version: ").append(android.os.Build.VERSION.RELEASE).append("\n");
            info.append("SDK Version: ").append(android.os.Build.VERSION.SDK_INT).append("\n");
            info.append("Device Model: ").append(android.os.Build.MODEL).append("\n");
            info.append("Device Manufacturer: ").append(android.os.Build.MANUFACTURER).append("\n");
            info.append("Package Name: ").append(context.getPackageName()).append("\n");
            
        } catch (Exception e) {
            info.append("Failed to get system info: ").append(e.getMessage()).append("\n");
        }
        
        return info.toString();
    }
}
