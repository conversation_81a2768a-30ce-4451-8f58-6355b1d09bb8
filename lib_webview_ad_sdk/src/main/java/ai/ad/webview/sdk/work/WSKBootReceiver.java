package ai.ad.webview.sdk.work;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSK开机自启动接收器
 * 监听系统开机广播，在设备重启后自动启动WSK工作管理器
 * 确保定时任务在设备重启后能够继续运行
 */
public class WSKBootReceiver extends BroadcastReceiver {
    private static final String TAG = "WSKBootReceiver";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        WSKLog.d(TAG, "onReceive -> Received broadcast: " + action);
        
        if (Intent.ACTION_BOOT_COMPLETED.equals(action) ||
            "android.intent.action.QUICKBOOT_POWERON".equals(action) ||
            "com.htc.intent.action.QUICKBOOT_POWERON".equals(action)) {
            
            WSKLog.i(TAG, "onReceive -> Device boot completed, starting WSK work");
            
            try {
                // 延迟启动，确保系统完全启动
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            // 等待5秒让系统完全启动
                            Thread.sleep(5000);
                            
                            // 启动WSK工作管理器
                            startWSKWork(context);
                            
                        } catch (InterruptedException e) {
                            WSKLog.e(TAG, "onReceive -> Thread interrupted: " + e.getMessage());
                        }
                    }
                }).start();
                
            } catch (Exception e) {
                WSKLog.e(TAG, "onReceive -> Exception occurred: " + e.getMessage());
            }
        }
    }
    
    /**
     * 启动WSK工作管理器
     */
    private void startWSKWork(Context context) {
        WSKLog.d(TAG, "startWSKWork -> Starting WSK work after boot");
        
        try {
            WSKWorkManager workManager = WSKWorkManager.getInstance();
            workManager.init(context);
            workManager.startWork();
            
            WSKLog.i(TAG, "startWSKWork -> WSK work started successfully after boot");
            
        } catch (Exception e) {
            WSKLog.e(TAG, "startWSKWork -> Failed to start WSK work after boot: " + e.getMessage());
        }
    }
}
