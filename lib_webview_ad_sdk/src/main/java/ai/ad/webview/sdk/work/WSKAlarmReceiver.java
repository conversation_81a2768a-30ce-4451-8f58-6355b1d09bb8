package ai.ad.webview.sdk.work;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.SystemClock;

import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSK闹钟接收器
 * 用于处理AlarmManager发送的定时广播
 * 负责执行WSK任务并重新设置下一次闹钟
 */
public class WSKAlarmReceiver extends BroadcastReceiver {
    private static final String TAG = "WSKAlarmReceiver";
    private static final long WORK_INTERVAL_MS = 60 * 1000L; // 1分钟
    
    @Override
    public void onReceive(Context context, Intent intent) {
        WSKLog.d(TAG, "onReceive -> Alarm triggered, executing WSK task");
        
        try {
            // 执行WSK任务
            executeWSKTask(context);
            
            // 重新设置下一次闹钟
            scheduleNextAlarm(context);
            
        } catch (Exception e) {
            WSKLog.e(TAG, "onReceive -> Exception occurred: " + e.getMessage());
        }
    }
    
    /**
     * 执行WSK任务
     */
    private void executeWSKTask(Context context) {
        WSKLog.d(TAG, "executeWSKTask -> Executing WSK task from alarm receiver");
        
        try {
            WSKWorkManager workManager = WSKWorkManager.getInstance();
            workManager.init(context);
            workManager.executeWSKTask();
            
        } catch (Exception e) {
            WSKLog.e(TAG, "executeWSKTask -> Failed to execute WSK task: " + e.getMessage());
        }
    }
    
    /**
     * 设置下一次闹钟
     */
    private void scheduleNextAlarm(Context context) {
        WSKLog.d(TAG, "scheduleNextAlarm -> Setting next alarm");
        
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager == null) {
            WSKLog.e(TAG, "scheduleNextAlarm -> AlarmManager is null");
            return;
        }
        
        Intent intent = new Intent(context, WSKAlarmReceiver.class);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context, 
            1001, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | 
            (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0)
        );
        
        // 设置下一次触发时间
        long triggerTime = SystemClock.elapsedRealtime() + WORK_INTERVAL_MS;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0+ 使用 setExactAndAllowWhileIdle
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME_WAKEUP, triggerTime, pendingIntent);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            // Android 4.4+ 使用 setExact
            alarmManager.setExact(AlarmManager.ELAPSED_REALTIME_WAKEUP, triggerTime, pendingIntent);
        } else {
            // Android 4.4以下使用 set
            alarmManager.set(AlarmManager.ELAPSED_REALTIME_WAKEUP, triggerTime, pendingIntent);
        }
        
        WSKLog.d(TAG, "scheduleNextAlarm -> Next alarm set for " + WORK_INTERVAL_MS + "ms later");
    }
}
