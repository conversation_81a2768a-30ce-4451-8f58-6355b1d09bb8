package ai.ad.webview.sdk.webview;

import android.app.Activity;
import android.os.Bundle;
import android.view.Window;

import ai.ad.webview.sdk.WSKSDK;
import ai.ad.webview.sdk.api.interfaces.IWSKActivity;
import ai.ad.webview.sdk.logger.WSKLog;

public class WSKActivity extends Activity {
    private static final String TAG = "WSKActivity";

    // 代理对象，负责实际的业务逻辑
    private IWSKActivity proxyActivity;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);

        super.onCreate(savedInstanceState);

        if (this.getWindow() == null) {
            finish();
            return;
        }

        // 创建代理类实例
        WSKLog.d(TAG, "onCreate -> Starting to create proxy class instance");
        proxyActivity = WSKSDK.createActivityProxy(this);
        if (proxyActivity == null) {
            WSKLog.e(TAG, "onCreate -> Cannot initialize WSKActivity: proxy class instance is null");
            finish();
            return;
        }

        // 配置窗口（由代理处理）
        WSKLog.d(TAG, "onCreate -> Configuring window through proxy");
        proxyActivity.configureWindow(getWindow());
        proxyActivity.onCreate(this);

        WSKLog.d(TAG, "onCreate -> WSKActivity initialization completed");
    }

    @Override
    protected void onStart() {
        super.onStart();
        WSKLog.d(TAG, "onStart -> WSKActivity started");
    }

    @Override
    protected void onResume() {
        super.onResume();
        WSKLog.d(TAG, "onResume -> WSKActivity resumed");
        if (proxyActivity != null) {
            proxyActivity.onResume(this);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        WSKLog.d(TAG, "onPause -> WSKActivity paused");
        if (proxyActivity != null) {
            proxyActivity.onPause(this);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        WSKLog.d(TAG, "onStop -> WSKActivity stopped");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        WSKLog.d(TAG, "onDestroy -> WSKActivity destroyed");
        if (proxyActivity != null) {
            proxyActivity.onDestroy(this);
        }
    }
}
