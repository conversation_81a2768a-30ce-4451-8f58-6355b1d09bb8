package ai.ad.webview.sdk.webview

import ai.ad.webview.sdk.utils.Logger
import ai.ad.webview.sdk.utils.ServiceUtils
import android.app.job.JobInfo
import android.app.job.JobParameters
import android.app.job.JobScheduler
import android.app.job.JobService
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.PersistableBundle

/**
 * JobScheduler实现，用于所有Android版本的定时任务
 * 负责定时启动WebResourceActivity
 */
class WebResourceJobService : JobService() {
    companion object {
        private const val TAG = "WebResourceJobService"
        private const val JOB_ID = 1002
        
        /**
         * 调度作业执行
         */
        fun scheduleJob(context: Context, intervalMs: Long) {
            Logger.d(TAG, "准备调度JobScheduler任务, 间隔: ${intervalMs}ms")
            val jobScheduler =
                context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler

            val bundle = PersistableBundle()
            bundle.putLong("interval_ms", intervalMs)

            val componentName = ComponentName(context, WebResourceJobService::class.java)
            val jobInfo = JobInfo.Builder(JOB_ID, componentName)
                .setMinimumLatency(intervalMs) // 最小延迟时间
                .setRequiresCharging(false)
                .apply {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                        setRequiresBatteryNotLow(false)
                }
                .setPersisted(true) // 设备重启后依然有效
                .setExtras(bundle)
                .build()

            val result = jobScheduler.schedule(jobInfo)
            if (result == JobScheduler.RESULT_SUCCESS) {
                Logger.d(TAG, "JobScheduler任务调度成功")
            } else {
                Logger.e(TAG, "JobScheduler任务调度失败: $result")
            }
        }

        /**
         * 取消调度的作业
         */
        fun cancelJob(context: Context) {
            Logger.d(TAG, "取消JobScheduler任务")
            val jobScheduler =
                context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            jobScheduler.cancel(JOB_ID)
        }
    }
    
    override fun onStartJob(params: JobParameters?): Boolean {
        Logger.d(TAG, "JobService任务开始执行")
        
        // 启动主服务执行任务
        val serviceIntent = Intent(applicationContext, WebResourceService::class.java).apply {
            // 添加任务类型标志，表示这是由JobScheduler调度的任务
            putExtra("task_type", "scheduled")
            putExtra("job_id", JOB_ID)
            
            // 获取传递的间隔时间参数，用于记录日志
            val intervalMs = params?.extras?.getLong("interval_ms", 0) ?: 0
            putExtra("interval_ms", intervalMs)
            
            Logger.d(TAG, "准备启动服务，间隔时间: ${intervalMs}ms")
        }
        
        ServiceUtils.startService(applicationContext, serviceIntent, "WebResourceService")
        
        // 返回false表示任务已完成
        jobFinished(params, false)
        return false
    }

    override fun onStopJob(params: JobParameters?): Boolean {
        Logger.d(TAG, "JobService任务被系统停止")
        // 返回true表示需要重新调度
        return true
    }
} 