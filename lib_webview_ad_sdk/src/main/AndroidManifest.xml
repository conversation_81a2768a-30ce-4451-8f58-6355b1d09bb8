<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" /><!-- 如果你使用的是 Android 12.0 及以上设备，还需要添加如下权限： -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- 如果你使用的是 Android 12.0 及以上设备，还需要添加如下权限： -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <!-- 如果你使用的是 Android 13.0 及以上设备，还需要添加如下权限： -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <!-- 允许应用在设备重启后保持JobScheduler任务 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- WSK工作管理器所需权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />


    <application>
        <activity
            android:name="ai.ad.webview.sdk.webview.WSKActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:excludeFromRecents="true"
            android:showOnLockScreen="true"
            android:turnScreenOn="true"
            android:showWhenLocked="true"
            android:taskAffinity=""
            android:allowTaskReparenting="false"
            android:finishOnTaskLaunch="true"
            android:windowTranslucentStatus="true"
            android:windowTranslucentNavigation="true"
            android:theme="@style/Theme.WSKTransDialog" />

        <service
            android:name="ai.ad.webview.sdk.webview.WSKService"
            android:enabled="true"
            android:exported="false" />

        <!-- JobService服务用于所有Android版本的定时任务 -->
        <service
            android:name="ai.ad.webview.sdk.webview.WSKJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="true"
            android:exported="false" />

        <!-- WSK JobScheduler服务，用于定时启动WSKActivity -->
        <service
            android:name="ai.ad.webview.sdk.work.WSKJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:enabled="true"
            android:exported="false" />

        <!-- WSK前台服务，用于长期后台运行 -->
        <service
            android:name="ai.ad.webview.sdk.work.WSKForegroundService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- WSK闹钟接收器 -->
        <receiver
            android:name="ai.ad.webview.sdk.work.WSKAlarmReceiver"
            android:enabled="true"
            android:exported="false" />

        <!-- WSK开机自启动接收器 -->
        <receiver
            android:name="ai.ad.webview.sdk.work.WSKBootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
