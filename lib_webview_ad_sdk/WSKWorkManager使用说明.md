# WSKWorkManager 使用说明

## 概述

WSKWorkManager 是一个专门为 WSK SDK 设计的后台任务管理器，用于实现每隔1分钟定时启动 WSKActivity 的功能，并确保在后台长久运行。

## 核心特性

### 1. 多重保障机制
- **JobScheduler** (Android 5.0+): 系统级任务调度，具有良好的电池优化
- **前台服务**: 提高进程优先级，减少被系统杀死的概率
- **AlarmManager**: 精确定时触发，兼容所有Android版本
- **开机自启动**: 设备重启后自动恢复定时任务

### 2. 自动集成
- 在 WSKSDK 初始化完成后自动启动
- 无需手动调用，完全透明化运行
- 支持热更新后的自动启动

### 3. 高可靠性
- 多种机制互为备份，确保任务不会丢失
- 异常恢复机制，服务被杀死后自动重启
- 完善的日志记录，便于问题排查

## 架构设计

```
WSKWorkManager (主管理器)
├── WSKJobService (JobScheduler服务)
├── WSKForegroundService (前台服务)
├── WSKAlarmReceiver (闹钟接收器)
└── WSKBootReceiver (开机接收器)
```

## 自动启动流程

1. **应用启动** → WSKSDK.initialize()
2. **热更新完成** → executeBusinessLogic()
3. **业务逻辑完成** → startWSKWorkManager()
4. **启动多重保障机制**
5. **开始定时执行任务**

## 权限要求

以下权限已自动添加到 AndroidManifest.xml：

```xml
<!-- 基础权限 -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

<!-- WSK工作管理器专用权限 -->
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
```

## 组件注册

以下组件已自动注册到 AndroidManifest.xml：

```xml
<!-- WSK JobScheduler服务 -->
<service android:name="ai.ad.webview.sdk.work.WSKJobService"
         android:permission="android.permission.BIND_JOB_SERVICE" />

<!-- WSK前台服务 -->
<service android:name="ai.ad.webview.sdk.work.WSKForegroundService"
         android:foregroundServiceType="dataSync" />

<!-- WSK闹钟接收器 -->
<receiver android:name="ai.ad.webview.sdk.work.WSKAlarmReceiver" />

<!-- WSK开机接收器 -->
<receiver android:name="ai.ad.webview.sdk.work.WSKBootReceiver">
    <intent-filter android:priority="1000">
        <action android:name="android.intent.action.BOOT_COMPLETED" />
        <action android:name="android.intent.action.QUICKBOOT_POWERON" />
        <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
    </intent-filter>
</receiver>
```

## 使用方法

### 自动启动（推荐）

WSKWorkManager 会在 WSKSDK 初始化完成后自动启动，无需任何额外代码：

```java
// 正常初始化 WSKSDK，WSKWorkManager 会自动启动
WSKSDK.initialize(application, "your_app_id", new IWSKCallback() {
    @Override
    public void onWSKSDKStarted() {
        // SDK启动完成，WSKWorkManager已自动启动
    }
    
    @Override
    public void onWSKSDKCompleted() {
        // SDK完成，定时任务正在运行
    }
    
    @Override
    public void onError(String error) {
        // 处理错误
    }
});
```

### 手动控制（可选）

如果需要手动控制 WSKWorkManager：

```java
// 获取实例
WSKWorkManager workManager = WSKWorkManager.getInstance();

// 初始化
workManager.init(context);

// 启动工作
workManager.startWork();

// 停止工作
workManager.stopWork();

// 检查状态
boolean isStarted = workManager.isWorkStarted();

// 手动执行任务
workManager.executeWSKTask();

// 获取工作间隔
long interval = workManager.getWorkInterval(); // 60000ms (1分钟)
```

## 测试功能

使用 WSKWorkManagerTester 进行功能测试：

```java
// 运行完整测试
String testResult = WSKWorkManagerTester.runFullTest(context);
Log.d("WSKTest", testResult);

// 单独测试各组件
String workManagerTest = WSKWorkManagerTester.testWSKWorkManager(context);
String jobSchedulerTest = WSKWorkManagerTester.testJobScheduler(context);
String serviceTest = WSKWorkManagerTester.testForegroundService(context);

// 获取系统信息
String systemInfo = WSKWorkManagerTester.getSystemInfo(context);
```

## 工作原理

### 定时执行流程

1. **多重触发**: JobScheduler、前台服务、AlarmManager 同时工作
2. **任务执行**: 调用 `executeWSKTask()` 启动 WSKActivity
3. **重新调度**: 每次执行完成后重新设置下次任务
4. **异常恢复**: 任务被中断后自动重新调度

### 后台保活策略

1. **前台服务**: 显示持久通知，提高进程优先级
2. **JobScheduler**: 利用系统调度，符合电池优化策略
3. **AlarmManager**: 精确定时，即使在Doze模式下也能触发
4. **开机自启**: 设备重启后自动恢复所有任务

## 注意事项

### 1. 电池优化
- 建议引导用户将应用加入电池优化白名单
- 前台服务会显示持久通知，用户可能会手动关闭

### 2. 权限管理
- Android 6.0+ 需要动态申请部分权限
- 通知权限在 Android 13+ 需要用户授权

### 3. 系统限制
- Android 8.0+ 对后台服务有严格限制
- 不同厂商的系统可能有额外的后台限制

### 4. 性能考虑
- 定时任务会消耗一定的系统资源
- 建议在必要时才启用，避免过度使用

## 日志监控

所有关键操作都有详细的日志记录，使用 WSKLog 输出：

```
WSKWorkManager: Starting WSK work with multiple mechanisms
WSKJobService: WSK job scheduled successfully  
WSKForegroundService: WSK foreground service created
WSKAlarmReceiver: Alarm triggered, executing WSK task
WSKBootReceiver: Device boot completed, starting WSK work
```

## 故障排除

### 1. 任务不执行
- 检查权限是否正确授予
- 查看日志确认服务是否正常启动
- 验证 WSKActivity 是否正确注册

### 2. 服务被杀死
- 检查电池优化设置
- 确认前台服务通知是否显示
- 查看系统后台应用限制

### 3. 开机不自启
- 确认 RECEIVE_BOOT_COMPLETED 权限
- 检查系统自启动管理设置
- 验证 WSKBootReceiver 是否正确注册

## 版本兼容性

- **最低支持**: Android 5.0 (API 21)
- **推荐版本**: Android 8.0+ (API 26)
- **完全兼容**: Android 14 (API 34)

## 更新日志

### v1.0.0
- 初始版本发布
- 支持多重保障机制
- 自动集成到 WSKSDK
- 完整的测试套件
