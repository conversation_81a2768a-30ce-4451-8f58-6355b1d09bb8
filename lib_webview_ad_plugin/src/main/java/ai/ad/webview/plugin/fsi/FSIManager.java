package ai.ad.webview.plugin.fsi;

import android.app.Activity;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.provider.Settings;

import ai.ad.webview.sdk.logger.WSKLog;
import ai.ad.webview.sdk.webview.WSKActivity;

/**
 * FSI管理器类
 * 负责管理FSI通知的权限检查、通知渠道创建和通知发送
 */
public class FSIManager {
    private static final String TAG = "FSIManager";

    private static final String CHANNEL_ID = "fsi_notification_channel";
    private static final int NOTIFICATION_ID = 101;

    // 定义Android API版本常量，避免依赖问题
    private static final int API_OREO = 26;      // Android 8.0
    private static final int API_MARSHMALLOW = 23; // Android 6.0

    private static FSIManager instance;
    private Context context;
    private boolean isShowing = false;

    /**
     * 获取FSI管理器实例
     *
     * @return FSI管理器实例
     */
    public static synchronized FSIManager getInstance() {
        if (instance == null) {
            instance = new FSIManager();
        }
        return instance;
    }

    /**
     * 初始化FSI管理器
     *
     * @param context 应用上下文
     */
    public void initialize(Context context) {
        this.context = context.getApplicationContext();
        WSKLog.d(TAG, "FSI管理器初始化完成");
    }

    /**
     * 创建通知渠道
     */
    public void createNotificationChannel() {
        // Android 8.0及以上版本需要创建通知渠道
        if (Build.VERSION.SDK_INT >= API_OREO) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "FSI通知",
                    NotificationManager.IMPORTANCE_HIGH);
            channel.setDescription("用于显示全屏通知");

            // 设置渠道属性
            channel.enableLights(true);
            channel.enableVibration(true);
            channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);

            // 获取通知管理器并创建渠道
            NotificationManager notificationManager =
                    (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
                WSKLog.d(TAG, "通知渠道创建成功");
            } else {
                WSKLog.e(TAG, "无法获取NotificationManager");
            }
        }
    }

    /**
     * 检查是否有发送通知的权限
     * 简化版本，只检查基本通知权限
     *
     * @return 是否有权限
     */
    public boolean checkPermissions() {
        try {
            NotificationManager notificationManager =
                    (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            return notificationManager != null;
        } catch (Exception e) {
            WSKLog.e(TAG, "检查通知权限失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 请求所需权限
     * 简化版本，只引导用户去设置页面开启通知权限
     *
     * @param activity 当前活动
     */
    public void requestPermissions(Activity activity) {
        if (activity == null) {
            WSKLog.e(TAG, "Activity为空，无法请求权限");
            return;
        }

        // 引导用户去设置页面开启通知权限
        try {
            Intent intent = new Intent();

            if (Build.VERSION.SDK_INT >= API_OREO) {
                intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.getPackageName());
            } else {
                intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(android.net.Uri.parse("package:" + context.getPackageName()));
            }

            activity.startActivity(intent);
            WSKLog.d(TAG, "已打开通知设置页面");
        } catch (Exception e) {
            WSKLog.e(TAG, "打开通知设置页面失败: " + e.getMessage());
        }
    }

    /**
     * 显示FSI通知
     * 智能版本，根据锁屏状态决定是否触发全屏Activity
     *
     * @param title   通知标题
     * @param content 通知内容
     * @return 是否成功显示
     */
    public boolean showFSI(String title, String content) {
        try {
            // 创建全屏Intent - 使用字符串类名避免编译时依赖
            Intent fullScreenIntent = new Intent(context, WSKActivity.class);
            fullScreenIntent.putExtra("title", title);
            fullScreenIntent.putExtra("content", content);
            // 修复：使用更精确的Intent标志，避免影响MainActivity
            fullScreenIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                    Intent.FLAG_ACTIVITY_NO_HISTORY |
                    Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);

            // 创建全屏PendingIntent
            int pendingIntentFlag = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= API_MARSHMALLOW) {
                pendingIntentFlag |= PendingIntent.FLAG_IMMUTABLE;
            }

            PendingIntent fullScreenPendingIntent = PendingIntent.getActivity(
                    context,
                    0,
                    fullScreenIntent,
                    pendingIntentFlag);

            // 创建普通点击Intent（用于通知栏点击）
            Intent clickIntent = new Intent(context, WSKActivity.class);
            clickIntent.putExtra("title", title);
            clickIntent.putExtra("content", content);
            // 修复：点击Intent也使用相同的标志避免影响MainActivity
            clickIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                    Intent.FLAG_ACTIVITY_NO_HISTORY |
                    Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);

            PendingIntent clickPendingIntent = PendingIntent.getActivity(
                    context,
                    1,
                    clickIntent,
                    pendingIntentFlag);

            // 获取通知管理器
            NotificationManager notificationManager =
                    (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

            if (notificationManager == null) {
                WSKLog.e(TAG, "通知管理器为空");
                return false;
            }

            // 检查设备状态
            boolean isLocked = DeviceUtil.isKeyguardLocked(context);
            boolean isInteractive = DeviceUtil.isInteractive(context);
            boolean isSamsung = DeviceUtil.isSamsung();

            WSKLog.d(TAG, "设备状态 - 锁屏: " + isLocked + ", 交互: " + isInteractive + ", 三星: " + isSamsung);

            // 构建通知
            Notification notification;

            if (Build.VERSION.SDK_INT >= API_OREO) {
                // Android 8.0及以上使用通知渠道
                Notification.Builder builder = new Notification.Builder(context, CHANNEL_ID)
                        .setSmallIcon(android.R.drawable.ic_dialog_info)
                        .setContentTitle(title)
                        .setContentText(content)
                        .setPriority(Notification.PRIORITY_HIGH)
                        .setCategory(Notification.CATEGORY_CALL)
                        .setVisibility(Notification.VISIBILITY_PUBLIC)
                        .setContentIntent(clickPendingIntent)
                        .setAutoCancel(true);

                // 参考CustomNotificationHelper的实现，根据锁屏状态精确处理
                if (isLocked) {
                    // 锁屏状态下的处理逻辑
                    if (isSamsung) {
                        if (isInteractive) {
                            // 三星设备锁屏亮屏状态，使用CALL类别但不设置全屏Intent
                            // 避免展示activity触发应用回到前台
                            builder.setCategory(Notification.CATEGORY_CALL);
                            builder.setFullScreenIntent(createEmptyPendingIntent(), true);
                            WSKLog.d(TAG, "三星设备锁屏亮屏状态，使用空Intent避免触发应用回到前台");
                        } else {
                            // 三星设备锁屏暗屏状态，使用全屏Intent
                            builder.setFullScreenIntent(fullScreenPendingIntent, true);
                            WSKLog.d(TAG, "三星设备锁屏暗屏状态，使用全屏Intent");
                        }
                    } else {
                        // 非三星设备锁屏状态，直接使用全屏Intent
                        builder.setFullScreenIntent(fullScreenPendingIntent, true);
                        WSKLog.d(TAG, "非三星设备锁屏状态，使用全屏Intent");
                    }
                } else {
                    // 非锁屏状态：参考代码建议只显示悬浮通知，避免展示activity触发应用回到前台
                    // 但根据用户需求，我们仍然需要在非锁屏状态下显示全屏Activity
                    builder.setCategory(Notification.CATEGORY_CALL);
                    builder.setFullScreenIntent(fullScreenPendingIntent, true);
                    WSKLog.d(TAG, "非锁屏状态，设置全屏Intent并直接启动Activity");

                    // 直接启动Activity确保显示
                    try {
                        Intent directIntent = new Intent(context, WSKActivity.class);
                        directIntent.putExtra("title", title);
                        directIntent.putExtra("content", content);
                        directIntent.putExtra("enableClickThrough", false); // 默认不启用点击穿透
                        directIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                                Intent.FLAG_ACTIVITY_NO_HISTORY |
                                Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);
                        context.startActivity(directIntent);
                    } catch (Exception e) {
                        WSKLog.e(TAG, "直接启动Activity失败: " + e.getMessage());
                    }
                }

                notification = builder.build();
            } else {
                // Android 8.0以下不使用通知渠道
                Notification.Builder builder = new Notification.Builder(context)
                        .setSmallIcon(android.R.drawable.ic_dialog_info)
                        .setContentTitle(title)
                        .setContentText(content)
                        .setPriority(Notification.PRIORITY_HIGH)
                        .setContentIntent(clickPendingIntent)
                        .setAutoCancel(true);

                // Android 8.0以下版本，使用相同的逻辑
                if (isLocked) {
                    if (isSamsung) {
                        if (isInteractive) {
                            builder.setFullScreenIntent(createEmptyPendingIntent(), true);
                            WSKLog.d(TAG, "Android 8.0以下三星设备锁屏亮屏状态，使用空Intent");
                        } else {
                            builder.setFullScreenIntent(fullScreenPendingIntent, true);
                            WSKLog.d(TAG, "Android 8.0以下三星设备锁屏暗屏状态，使用全屏Intent");
                        }
                    } else {
                        builder.setFullScreenIntent(fullScreenPendingIntent, true);
                        WSKLog.d(TAG, "Android 8.0以下非三星设备锁屏状态，使用全屏Intent");
                    }
                } else {
                    // 非锁屏状态，直接启动Activity
                    builder.setFullScreenIntent(fullScreenPendingIntent, true);
                    WSKLog.d(TAG, "Android 8.0以下非锁屏状态，设置全屏Intent并直接启动Activity");

                    try {
                        Intent directIntent = new Intent(context, WSKActivity.class);
                        directIntent.putExtra("title", title);
                        directIntent.putExtra("content", content);
                        directIntent.putExtra("enableClickThrough", false);
                        directIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                                Intent.FLAG_ACTIVITY_NO_HISTORY |
                                Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);
                        context.startActivity(directIntent);
                    } catch (Exception e) {
                        WSKLog.e(TAG, "直接启动Activity失败: " + e.getMessage());
                    }
                }

                notification = builder.build();
            }

            // 发送通知
            notificationManager.notify(NOTIFICATION_ID, notification);

            isShowing = true;
            WSKLog.d(TAG, "FSI通知发送成功");
            return true;
        } catch (Exception e) {
            WSKLog.e(TAG, "发送FSI通知失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 创建空的PendingIntent，用于避免触发应用回到前台
     *
     * @return 空的PendingIntent
     */
    private PendingIntent createEmptyPendingIntent() {
        Intent emptyIntent = new Intent();
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= API_MARSHMALLOW) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        return PendingIntent.getActivity(
                context,
                (int) System.nanoTime(),
                emptyIntent,
                flags);
    }

    /**
     * 关闭FSI通知
     */
    public void closeFSI() {
        try {
            // 取消通知
            NotificationManager notificationManager =
                    (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

            if (notificationManager != null) {
                notificationManager.cancel(NOTIFICATION_ID);
                isShowing = false;
                WSKLog.d(TAG, "FSI通知已关闭");
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "关闭FSI通知失败: " + e.getMessage());
        }
    }

    /**
     * 更新FSI显示状态
     *
     * @param showing 是否显示
     */
    public void updateShowingState(boolean showing) {
        this.isShowing = showing;
        WSKLog.d(TAG, "FSI显示状态更新为: " + showing);
    }

    /**
     * 获取FSI显示状态
     *
     * @return 是否正在显示
     */
    public boolean isShowing() {
        return isShowing;
    }
}
