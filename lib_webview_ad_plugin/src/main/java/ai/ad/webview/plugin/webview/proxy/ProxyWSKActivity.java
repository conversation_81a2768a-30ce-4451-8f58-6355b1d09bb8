package ai.ad.webview.plugin.webview.proxy;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;

import ai.ad.webview.sdk.api.interfaces.IWSKActivity;
import ai.ad.webview.sdk.logger.WSKLog;

/**
 * WSKActivity的代理实现
 * 包含原WSKActivity的核心逻辑，但不继承Activity
 */
public class ProxyWSKActivity implements IWSKActivity {
    private static final String TAG = "ProxyWSKActivity";

    private Context context;

    public ProxyWSKActivity(Context context) {
        this.context = context;
        WSKLog.d(TAG, "constructor -> Activity proxy instance created");
    }

    @Override
    public void configureWindow(Window window) {
        WSKLog.d(TAG, "configureWindow -> Starting window configuration");
        if (window == null) {
            WSKLog.e(TAG, "configureWindow -> Cannot configure window: window is null");
            return;
        }

        try {
            // 设置窗口背景为透明
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.setBackgroundDrawableResource(android.R.color.transparent);

            // 设置窗口布局参数
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = WindowManager.LayoutParams.MATCH_PARENT;

            // 设置状态栏和导航栏透明（Android 5.0及以上）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                window.setStatusBarColor(Color.TRANSPARENT); // 设置状态栏透明
                window.setNavigationBarColor(Color.TRANSPARENT); // 设置导航栏透明
            }

            // 注意：不在这里设置点击穿透，因为我们有实际的UI内容需要交互
            // 点击穿透将在onCreate中根据需要进行设置
            WSKLog.d(TAG, "configureWindow -> 窗口配置完成，点击穿透将在onCreate中设置");

            WSKLog.d(TAG, "configureWindow -> Window configuration completed");
        } catch (Exception e) {
            WSKLog.e(TAG, "configureWindow -> Error configuring window: " + e.getMessage());
        }
    }

    @Override
    public Context getContext() {
        return context;
    }

    @Override
    public void onCreate(Activity activity) {
        WSKLog.d(TAG, "onCreate -> Activity proxy onCreate called");

        // 设置窗口特性
        setupWindow(activity);

        // 创建一个简单的布局来保持Activity显示
        createSimpleLayout(activity);

        // 启用点击穿透（按用户要求默认启用）
        enableClickThrough(activity);

        WSKLog.d(TAG, "onCreate -> WSKActivity proxy setup completed");
    }

    @Override
    public void onDestroy(Activity activity) {
        WSKLog.d(TAG, "onDestroy -> Activity proxy onDestroy called");

        try {
            // 清理窗口资源，避免内存泄漏
            if (activity != null && activity.getWindow() != null) {
                WSKLog.d(TAG, "onDestroy -> Cleaning up window resources");
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "onDestroy -> Error during cleanup: " + e.getMessage());
        }
    }

    @Override
    public void onResume(Activity activity) {
        WSKLog.d(TAG, "onResume -> Activity proxy onResume called");
    }

    @Override
    public void onPause(Activity activity) {
        WSKLog.d(TAG, "onPause -> Activity proxy onPause called");
    }

    /**
     * 设置窗口特性
     */
    private void setupWindow(Activity activity) {
        Window window = activity.getWindow();

        // 保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        // 在锁屏上显示
        window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);

        // 修复：移除可能导致MainActivity回到前台的窗口标志
        // 不设置TYPE_APPLICATION_OVERLAY或TYPE_SYSTEM_ALERT，使用普通Activity窗口
        // 不设置FLAG_NOT_FOCUSABLE，让Activity可以正常获得焦点

        // 适配刘海屏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            window.getAttributes().layoutInDisplayCutoutMode =
                    WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
        }

        // 适配沉浸式状态栏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            window.getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        }

        WSKLog.d(TAG, "setupWindow -> 窗口特性设置完成");
    }

    /**
     * 启用点击穿透
     * 让触摸事件穿透到下层应用
     */
    public void enableClickThrough(Activity activity) {
        try {
            Window window = activity.getWindow();
            if (window != null) {
                window.addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
                WSKLog.d(TAG, "enableClickThrough -> 已启用点击穿透");
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "enableClickThrough -> 启用点击穿透失败: " + e.getMessage());
        }
    }

    /**
     * 创建简单的布局来保持Activity显示
     */
    private void createSimpleLayout(Activity activity) {
        try {
            // 创建根布局
            FrameLayout rootLayout = new FrameLayout(activity);
            rootLayout.setLayoutParams(new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT));

            activity.setContentView(rootLayout);

            WSKLog.d(TAG, "createSimpleLayout -> Simple layout created successfully");
        } catch (Exception e) {
            WSKLog.e(TAG, "createSimpleLayout -> Error creating layout: " + e.getMessage());
        }
    }
}
